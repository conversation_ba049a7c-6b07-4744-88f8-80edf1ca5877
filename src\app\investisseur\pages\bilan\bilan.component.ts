import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../auth/auth.service';
import { ReportService, ReportData } from '../../services/report.service';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { PanelModule } from 'primeng/panel';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

@Component({
  selector: 'app-bilan',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    CardModule,
    PanelModule,
    TableModule,
    TagModule,
    ProgressBarModule,
    ToastModule
  ],
  templateUrl: './bilan.component.html',
  styleUrl: './bilan.component.css',
  providers: [MessageService]
})
export class BilanComponent implements OnInit {
  reportData: ReportData | null = null;
  loading: boolean = false;
  username: string = '';

  constructor(
    private reportService: ReportService,
    private authService: AuthService,
    private messageService: MessageService
  ) { }

  ngOnInit(): void {
    this.username = this.authService.getUsername();
    this.loadReportData();
  }

  loadReportData(): void {
    this.loading = true;
    this.reportService.getReportData(this.username)
      .subscribe({
        next: (data: ReportData) => {
          if (this.reportService.validateReportData(data)) {
            this.reportData = data;
            this.messageService.add({
              severity: 'success',
              summary: 'Succès',
              detail: 'Données du rapport chargées avec succès'
            });
          } else {
            this.messageService.add({
              severity: 'warn',
              summary: 'Attention',
              detail: 'Données du rapport incomplètes'
            });
          }
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Erreur lors du chargement des données:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Erreur',
            detail: 'Impossible de charger les données du rapport'
          });
          this.loading = false;
        }
      });
  }

  async exportToPDF(): Promise<void> {
    if (!this.reportData) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Attention',
        detail: 'Aucune donnée disponible pour l\'export'
      });
      return;
    }

    try {
      const doc = new jsPDF();
      let logoBase64: string | undefined;

      // Charger le logo
      try {
        logoBase64 = await this.reportService.getImageAsBase64('assets/logo.png');
      } catch (error) {
        console.warn('Impossible de charger le logo:', error);
      }

      // Créer l'en-tête ultra-moderne
      let yPosition = this.reportService.createModernHeader(doc, logoBase64);

      // Section Informations Investisseur
      yPosition = this.reportService.createModernSection(doc, 'INFORMATIONS INVESTISSEUR', yPosition);

      const investisseur = this.reportData.investisseur;
      const pageWidth = doc.internal.pageSize.width;

      // Cartes d'information modernes
      const cardWidth = (pageWidth - 60) / 2;
      const cardHeight = 30;

      this.reportService.createInfoCard(
        doc,
        'NOM COMPLET',
        `${investisseur.Nom} ${investisseur.Prenom}`,
        20,
        yPosition,
        cardWidth,
        cardHeight
      );

      this.reportService.createInfoCard(
        doc,
        'EMAIL',
        investisseur.Email,
        30 + cardWidth,
        yPosition,
        cardWidth,
        cardHeight
      );

      yPosition += cardHeight + 10;

      this.reportService.createInfoCard(
        doc,
        'BALANCE DISPONIBLE',
        `${(investisseur['Balance disponible'] || 0).toLocaleString('fr-FR')} TND`,
        20,
        yPosition,
        cardWidth,
        cardHeight
      );

      this.reportService.createInfoCard(
        doc,
        'PORTEFEUILLES DÉTENUS',
        `${investisseur['Portefeuilles détenus']} portefeuille(s)`,
        30 + cardWidth,
        yPosition,
        cardWidth,
        cardHeight
      );

      yPosition += cardHeight + 20;

      // Section Portefeuilles
      if (this.reportData.portefeuilles && this.reportData.portefeuilles.length > 0) {
        yPosition = this.reportService.createModernSection(doc, 'DÉTAIL DES PORTEFEUILLES', yPosition);

        this.reportData.portefeuilles.forEach((portefeuille) => {
          // Vérifier si on a besoin d'une nouvelle page
          if (yPosition > 220) {
            doc.addPage();
            this.reportService.addModernFooter(doc, doc.getNumberOfPages() - 1, doc.getNumberOfPages());
            yPosition = this.reportService.createModernHeader(doc, logoBase64);
          }

          // Titre du portefeuille avec style moderne
          doc.setFillColor(248, 250, 252);
          doc.roundedRect(20, yPosition - 5, pageWidth - 40, 20, 3, 3, 'F');

          doc.setTextColor(30, 41, 59);
          doc.setFontSize(14);
          doc.setFont('helvetica', 'bold');
          doc.text(`Portefeuille: ${portefeuille.nom}`, 25, yPosition + 7);

          // Statistiques du portefeuille
          const valeurTotale = `${(portefeuille.valeurTotale || 0).toLocaleString('fr-FR')} TND`;
          const performance = `${(portefeuille.performance || 0).toFixed(2)}%`;
          const performanceColor: [number, number, number] = portefeuille.performance >= 0 ? [34, 197, 94] : [239, 68, 68];

          doc.setTextColor(performanceColor[0], performanceColor[1], performanceColor[2]);
          doc.setFontSize(10);
          doc.text(`Valeur: ${valeurTotale} | Performance: ${performance}`, pageWidth - 25, yPosition + 7, { align: 'right' });

          yPosition += 25;

          // Tableau des titres avec design moderne
          if (portefeuille.titres && portefeuille.titres.length > 0) {
            const titresData = portefeuille.titres.map((titre) => [
              titre.nom,
              titre.quantite.toString(),
              `${titre.valeurActuelle.toFixed(2)} TND`,
              `${this.reportService.calculateTotalValue(titre.quantite, titre.valeurActuelle).toFixed(2)} TND`
            ]);

            yPosition = this.reportService.createModernTable(
              doc,
              ['Titre', 'Quantité', 'Valeur Unitaire', 'Valeur Totale'],
              titresData,
              yPosition
            );
          }

          // Espacement entre portefeuilles
          yPosition += 10;
        });
      }

      // Ajouter le pied de page final
      this.reportService.addModernFooter(doc, 1, 1);

      // Sauvegarder le PDF avec nom personnalisé
      const fileName = this.reportService.generatePdfFileName(this.username);
      doc.save(fileName);

      this.messageService.add({
        severity: 'success',
        summary: 'Succès',
        detail: 'Rapport PDF ultra-moderne exporté avec succès'
      });

    } catch (error) {
      console.error('Erreur lors de l\'export PDF:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Erreur',
        detail: 'Erreur lors de l\'export du rapport PDF'
      });
    }
  }

  getPerformanceClass(performance: number): string {
    return this.reportService.getPerformanceClass(performance);
  }

  getPerformanceIcon(performance: number): string {
    return this.reportService.getPerformanceIcon(performance);
  }
}
