/* ===== VARIABLES CSS ===== */
:root {
    --primary-blue: #2563eb;
    --primary-green: #059669;
    --secondary-blue: #3b82f6;
    --secondary-green: #10b981;
    --light-blue: #dbeafe;
    --light-green: #d1fae5;
    --dark-blue: #1e40af;
    --dark-green: #047857;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --background-light: #f8fafc;
    --background-white: #ffffff;
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* ===== STYLES GÉNÉRAUX ===== */
.bilan-header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: white;
    padding: 2rem;
    border-radius: var(--radius-xl);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.bilan-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.header-left {
    flex: 1;
}

.bilan-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.title-icon {
    font-size: 1.75rem;
    color: var(--light-blue);
}

.bilan-subtitle {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 400;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* ===== BOUTONS D'ACTION ===== */
.export-btn {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%) !important;
    border: none !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: var(--radius-lg) !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: var(--shadow-md) !important;
}

.export-btn:hover:not(:disabled) {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
    background: linear-gradient(135deg, var(--dark-green) 0%, var(--primary-green) 100%) !important;
}

.refresh-btn {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: var(--radius-lg) !important;
    font-weight: 600 !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.refresh-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
}

/* ===== CONTENU PRINCIPAL ===== */
.bilan-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.report-section {
    background: var(--background-white);
    border-radius: var(--radius-xl);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.report-section:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-light);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-title i {
    color: var(--primary-blue);
    font-size: 1.25rem;
}

/* ===== GRILLE INFORMATIONS INVESTISSEUR ===== */
.investor-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.info-card {
    background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-green) 100%);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.info-card::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-green) 100%);
    border-radius: 50%;
    opacity: 0.1;
    z-index: 0;
}

.info-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.info-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.info-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
}

.balance-value {
    color: var(--primary-green);
    font-size: 1.5rem;
}

/* ===== CONTENEUR PORTEFEUILLES ===== */
.portfolios-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.portfolio-card {
    background: var(--background-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.portfolio-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.portfolio-header {
    background: linear-gradient(135deg, var(--light-blue) 0%, var(--light-green) 100%);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.portfolio-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse"><path d="M 60 0 L 0 0 0 60" fill="none" stroke="rgba(37,99,235,0.1)" stroke-width="1"/></pattern></defs><rect width="60" height="60" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.portfolio-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    z-index: 1;
}

.portfolio-name i {
    color: var(--primary-blue);
    font-size: 1.125rem;
}

.portfolio-stats {
    display: flex;
    gap: 2rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* ===== CLASSES DE PERFORMANCE ===== */
.performance-positive {
    color: var(--primary-green) !important;
}

.performance-negative {
    color: #dc2626 !important;
}

.performance-neutral {
    color: var(--text-secondary) !important;
}

.portfolio-content {
    padding: 1.5rem;
}

.subsection-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.subsection-title i {
    color: var(--primary-blue);
}

/* ===== STYLES TABLEAUX ===== */
::ng-deep .modern-table {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

::ng-deep .modern-table .p-datatable-header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: white;
    border: none;
    padding: 1rem;
}

::ng-deep .modern-table .p-datatable-thead>tr>th {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

::ng-deep .modern-table .p-datatable-tbody>tr {
    transition: all 0.2s ease;
}

::ng-deep .modern-table .p-datatable-tbody>tr:hover {
    background: var(--light-blue);
    transform: scale(1.01);
}

::ng-deep .modern-table .p-datatable-tbody>tr>td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-light);
    font-size: 0.875rem;
}

.titre-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.titre-icon {
    color: var(--primary-blue);
    font-size: 0.875rem;
}

.titre-name {
    font-weight: 600;
    color: var(--text-primary);
}

.price-value,
.total-value {
    font-weight: 600;
    color: var(--text-primary);
}

::ng-deep .quantity-tag {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
    color: white !important;
    border-radius: var(--radius-md) !important;
    padding: 0.375rem 0.75rem !important;
    font-weight: 600 !important;
}

::ng-deep .type-tag {
    border-radius: var(--radius-md) !important;
    padding: 0.375rem 0.75rem !important;
    font-weight: 600 !important;
}

/* ===== ÉTATS DE CHARGEMENT ET VIDE ===== */
.loading-container,
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: var(--background-white);
    border-radius: var(--radius-xl);
    margin: 2rem auto;
    max-width: 1200px;
    box-shadow: var(--shadow-md);
}

.loading-content,
.empty-content {
    text-align: center;
    padding: 2rem;
}

.loading-icon,
.empty-icon {
    font-size: 3rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.loading-text,
.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.empty-message {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.retry-btn {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
    border: none !important;
    color: white !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: var(--radius-lg) !important;
    font-weight: 600 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.retry-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 1199px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
    }

    .portfolio-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .portfolio-stats {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .bilan-header {
        padding: 1.5rem;
    }

    .bilan-title {
        font-size: 1.5rem;
    }

    .report-section {
        padding: 1.5rem;
    }

    .investor-info-grid {
        grid-template-columns: 1fr;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .export-btn,
    .refresh-btn {
        width: 100% !important;
    }

    .portfolio-stats {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 575px) {
    .bilan-content {
        padding: 0 0.5rem;
    }

    .report-section {
        padding: 1rem;
    }

    .portfolio-content {
        padding: 1rem;
    }

    ::ng-deep .modern-table .p-datatable-tbody>tr>td,
    ::ng-deep .modern-table .p-datatable-thead>tr>th {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }
}